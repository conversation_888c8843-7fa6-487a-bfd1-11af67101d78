import { createClient } from "@/lib/supabase/server"
import { redirect } from "next/navigation"
import Link from "next/link"
import { format } from "date-fns"
import { vi } from "date-fns/locale"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Progress } from "@/components/ui/progress"
import {
  FileText,
  Pill,
  Activity,
  MessageSquare,
  ChevronRight,
  Heart,
  Thermometer,
  TrendingUp,
  User,
  Check,
  BarChart3,
  ClipboardList,
  HelpCircle,
  FileQuestion,
  MessageCircle,
  Clock,
  Calendar,
} from "lucide-react"

export default async function DashboardPage() {
  const supabase = createClient()

  const {
    data: { session },
  } = await supabase.auth.getSession()

  if (!session) {
    redirect("/login")
  }

  // Get the authenticated user
  const { data: userData, error: userError } = await supabase
    .from("users")
    .select("*")
    .eq("id", session.user.id)
    .single()

  if (userError || !userData) {
    console.error("Error fetching user data:", userError)
    redirect("/login")
  }

  // Fetch additional data based on user role
  let roleSpecificData = null
  if (userData.role === "patient") {
    const { data: patientData } = await supabase.from("patients").select("*").eq("id", userData.id).single()
    roleSpecificData = patientData
  } else if (userData.role === "medical_staff") {
    const { data: medicalStaffData } = await supabase.from("medical_staff").select("*").eq("user_id", userData.id).single()
    roleSpecificData = medicalStaffData
  }

  // Fetch advisory requests for patient or doctor
  let advisoryRequests = []
  if (userData.role === "patient") {
    const { data: advisories } = await supabase
      .from("advisory_requests")
      .select(`
        id,
        title,
        created_at,
        status,
        urgency_level,
        assigned_doctor_id
      `)
      .eq("patient_id", userData.id)
      .order("created_at", { ascending: false })
      .limit(3)

    advisoryRequests = advisories || []
  } else if (userData.role === "medical_staff" && roleSpecificData) {
    const { data: advisories } = await supabase
      .from("advisory_requests")
      .select(`
        id,
        title,
        created_at,
        status,
        urgency_level,
        patient_id,
        patients!inner(full_name)
      `)
      .eq("assigned_doctor_id", roleSpecificData.id)
      .order("created_at", { ascending: false })
      .limit(3)

    advisoryRequests = advisories || []
  }

  // Fetch active prescriptions for patient or linked patients for doctor
  let activePrescriptions = []
  if (userData.role === "patient") {
    const { data: prescriptions } = await supabase
      .from("prescriptions")
      .select(`
        id,
        prescribed_date,
        status,
        prescription_medications(id, medication_name, dosage, frequency, instructions)
      `)
      .eq("patient_id", userData.id)
      .eq("status", "active")
      .order("prescribed_date", { ascending: false })
      .limit(3)

    activePrescriptions = prescriptions || []
  } else if (userData.role === "medical_staff" && roleSpecificData) {
    const { data: prescriptions } = await supabase
      .from("prescriptions")
      .select(`
        id,
        prescribed_date,
        status,
        patient_id,
        patients!inner(full_name),
        prescription_medications(id, medication_name, dosage, frequency, instructions)
      `)
      .eq("prescribed_by", roleSpecificData.id)
      .eq("status", "active")
      .order("prescribed_date", { ascending: false })
      .limit(3)

    activePrescriptions = prescriptions || []
  }

  // Fetch recent health metrics for patient
  let recentHealthMetrics = []
  if (userData.role === "patient") {
    const { data: metrics } = await supabase
      .from("health_metrics")
      .select("*")
      .eq("patient_id", userData.id)
      .order("recorded_at", { ascending: false })
      .limit(4)

    recentHealthMetrics = metrics || []
  }

  // Fetch doctor-specific data
  let linkedPatientsCount = 0
  let todayConsultationsCount = 0
  let pendingAdvisoryCount = 0
  let activePrescriptionsCount = 0
  let weeklyConsultationsCount = 0
  let completedAdvisoryCount = 0

  if (userData.role === "medical_staff" && roleSpecificData) {
    // Get linked patients count
    const { count: patientsCount } = await supabase
      .from("doctor_patient_links")
      .select("*", { count: "exact", head: true })
      .eq("doctor_id", roleSpecificData.id)
      .eq("status", "approved")

    linkedPatientsCount = patientsCount || 0

    // Get today's consultations count
    const today = new Date().toISOString().split('T')[0]
    const { count: consultationsCount } = await supabase
      .from("consultations")
      .select("*", { count: "exact", head: true })
      .eq("doctor_id", roleSpecificData.id)
      .gte("scheduled_at", `${today}T00:00:00`)
      .lt("scheduled_at", `${today}T23:59:59`)

    todayConsultationsCount = consultationsCount || 0

    // Get this week's consultations count
    const weekStart = new Date()
    weekStart.setDate(weekStart.getDate() - weekStart.getDay())
    const { count: weeklyCount } = await supabase
      .from("consultations")
      .select("*", { count: "exact", head: true })
      .eq("doctor_id", roleSpecificData.id)
      .gte("scheduled_at", weekStart.toISOString())

    weeklyConsultationsCount = weeklyCount || 0

    // Get pending advisory requests count
    const { count: pendingCount } = await supabase
      .from("advisory_requests")
      .select("*", { count: "exact", head: true })
      .eq("assigned_doctor_id", roleSpecificData.id)
      .in("status", ["pending", "assigned", "in_progress"])

    pendingAdvisoryCount = pendingCount || 0

    // Get completed advisory requests count
    const { count: completedCount } = await supabase
      .from("advisory_requests")
      .select("*", { count: "exact", head: true })
      .eq("assigned_doctor_id", roleSpecificData.id)
      .eq("status", "completed")

    completedAdvisoryCount = completedCount || 0

    // Get active prescriptions count
    const { count: prescriptionsCount } = await supabase
      .from("prescriptions")
      .select("*", { count: "exact", head: true })
      .eq("prescribed_by", roleSpecificData.id)
      .eq("status", "active")

    activePrescriptionsCount = prescriptionsCount || 0
  }

  // Process health metrics for display
  const processedHealthMetrics = recentHealthMetrics.map((metric) => ({
    name: metric.metric_type,
    value: metric.value,
    unit: metric.unit || "",
    status: "normal", // You can add logic to determine status based on value ranges
    recorded_at: metric.recorded_at,
    icon: getMetricIcon(metric.metric_type),
  }))

  // Get icon for metric type
  function getMetricIcon(metricType: string) {
    switch (metricType?.toLowerCase()) {
      case "blood_pressure":
      case "huyết áp":
        return <Heart className="h-4 w-4 text-rose-500" />
      case "heart_rate":
      case "nhịp tim":
        return <Activity className="h-4 w-4 text-emerald-500" />
      case "temperature":
      case "nhiệt độ":
        return <Thermometer className="h-4 w-4 text-amber-500" />
      case "weight":
      case "cân nặng":
        return <TrendingUp className="h-4 w-4 text-blue-500" />
      default:
        return <Activity className="h-4 w-4 text-blue-500" />
    }
  }

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return "Chưa xác định"
    return format(new Date(dateString), "dd/MM/yyyy", { locale: vi })
  }

  // Format time for display
  const formatTime = (dateString) => {
    if (!dateString) return ""
    return format(new Date(dateString), "HH:mm", { locale: vi })
  }

  // Get status color
  const getStatusColor = (status) => {
    switch (status) {
      case "normal":
        return "text-emerald-500"
      case "warning":
        return "text-amber-500"
      case "critical":
        return "text-rose-500"
      case "improving":
        return "text-blue-500"
      default:
        return "text-gray-500"
    }
  }

  // Get advisory status badge
  const getAdvisoryStatusBadge = (status) => {
    switch (status) {
      case "pending":
        return (
          <Badge variant="outline" className="bg-amber-50 text-amber-700">
            Đang chờ
          </Badge>
        )
      case "assigned":
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700">
            Đã phân công
          </Badge>
        )
      case "in_progress":
        return (
          <Badge variant="outline" className="bg-purple-50 text-purple-700">
            Đang xử lý
          </Badge>
        )
      case "completed":
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700">
            Hoàn thành
          </Badge>
        )
      case "cancelled":
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700">
            Đã hủy
          </Badge>
        )
      default:
        return <Badge variant="outline">Không xác định</Badge>
    }
  }

  // Get urgency level badge
  const getUrgencyBadge = (level) => {
    switch (level) {
      case "low":
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Thấp</Badge>
      case "medium":
        return <Badge className="bg-amber-100 text-amber-800 hover:bg-amber-100">Trung bình</Badge>
      case "high":
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-100">Cao</Badge>
      default:
        return <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-100">Không xác định</Badge>
    }
  }

  return (
    <div className="container mx-auto py-4">
      {/* Welcome section - Simplified */}
      <div className="mb-6 flex flex-col space-y-2">
        <h1 className="text-2xl font-bold">Xin chào, {userData.full_name}</h1>
        <p className="text-muted-foreground">Chào mừng bạn quay trở lại với OncoCare.VN</p>
      </div>

      {userData.role === "patient" && (
        <>
          {/* Main dashboard content - Simplified with tabs */}
          <Tabs defaultValue="overview" className="mb-8">
            <TabsList className="mb-4 grid w-full grid-cols-3 md:w-auto">
              <TabsTrigger value="overview">Tổng quan</TabsTrigger>
              <TabsTrigger value="advisory">Tư vấn</TabsTrigger>
              <TabsTrigger value="medications">Thuốc</TabsTrigger>
            </TabsList>

            {/* Overview Tab */}
            <TabsContent value="overview" className="space-y-4">
              {/* Patient summary - Simplified */}
              {roleSpecificData && (
                <Card className="border-l-4 border-l-blue-500">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">Thông tin bệnh nhân</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                      <div>
                        <p className="text-sm font-medium">Chẩn đoán</p>
                        <p className="text-sm text-muted-foreground">
                          {roleSpecificData?.diagnosis || "Chưa có thông tin"}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm font-medium">Loại ung thư</p>
                        <p className="text-sm text-muted-foreground">
                          {roleSpecificData?.cancer_type || "Chưa có thông tin"}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm font-medium">Giai đoạn</p>
                        <p className="text-sm text-muted-foreground">
                          {roleSpecificData?.cancer_stage || "Chưa có thông tin"}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm font-medium">Trạng thái điều trị</p>
                        <p className="text-sm text-muted-foreground">
                          {roleSpecificData?.treatment_status || "Chưa có thông tin"}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Quick Actions - Simplified */}
              <div className="grid grid-cols-2 gap-4 md:grid-cols-4">
                <Link href="/advisory/request" className="group">
                  <Card className="h-full transition-all hover:border-blue-500 hover:shadow-md">
                    <CardContent className="flex flex-col items-center justify-center p-4 text-center">
                      <HelpCircle className="mb-2 h-8 w-8 text-blue-500" />
                      <p className="font-medium">Yêu cầu tư vấn</p>
                    </CardContent>
                  </Card>
                </Link>
                <Link href="/prescriptions" className="group">
                  <Card className="h-full transition-all hover:border-blue-500 hover:shadow-md">
                    <CardContent className="flex flex-col items-center justify-center p-4 text-center">
                      <Pill className="mb-2 h-8 w-8 text-blue-500" />
                      <p className="font-medium">Đơn thuốc</p>
                    </CardContent>
                  </Card>
                </Link>
                <Link href="/medical-records" className="group">
                  <Card className="h-full transition-all hover:border-blue-500 hover:shadow-md">
                    <CardContent className="flex flex-col items-center justify-center p-4 text-center">
                      <FileText className="mb-2 h-8 w-8 text-blue-500" />
                      <p className="font-medium">Hồ sơ y tế</p>
                    </CardContent>
                  </Card>
                </Link>
                <Link href="/health-metrics" className="group">
                  <Card className="h-full transition-all hover:border-blue-500 hover:shadow-md">
                    <CardContent className="flex flex-col items-center justify-center p-4 text-center">
                      <BarChart3 className="mb-2 h-8 w-8 text-blue-500" />
                      <p className="font-medium">Chỉ số sức khỏe</p>
                    </CardContent>
                  </Card>
                </Link>
              </div>

              {/* Health Metrics - Real data only */}
              {processedHealthMetrics.length > 0 && (
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="flex items-center text-lg">
                      <Activity className="mr-2 h-5 w-5 text-blue-500" />
                      Chỉ số sức khỏe gần đây
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid gap-4 md:grid-cols-2">
                      {processedHealthMetrics.map((metric, index) => (
                        <div key={index} className="flex items-center justify-between rounded-lg border p-3">
                          <div className="flex items-center gap-2">
                            {metric.icon}
                            <span className="font-medium">{metric.name}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <span className={getStatusColor(metric.status)}>
                              {metric.value} {metric.unit}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                    <div className="mt-4 flex justify-end">
                      <Button variant="ghost" size="sm" asChild>
                        <Link href="/health-metrics" className="flex items-center">
                          Xem tất cả
                          <ChevronRight className="ml-1 h-4 w-4" />
                        </Link>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Treatment Progress - Only show if patient has treatment data */}
              {roleSpecificData && (roleSpecificData.treatment_status || roleSpecificData.cancer_stage) && (
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="flex items-center text-lg">
                      <Activity className="mr-2 h-5 w-5 text-blue-500" />
                      Thông tin điều trị
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {roleSpecificData.treatment_status && (
                        <div className="space-y-2">
                          <p className="font-medium">Trạng thái điều trị: {roleSpecificData.treatment_status}</p>
                          <p className="text-sm text-muted-foreground">
                            {roleSpecificData.cancer_stage && `Giai đoạn: ${roleSpecificData.cancer_stage}`}
                          </p>
                        </div>
                      )}

                      <div className="mt-4 flex justify-end">
                        <Button variant="ghost" size="sm" asChild>
                          <Link href="/medical-records" className="flex items-center">
                            Xem hồ sơ y tế
                            <ChevronRight className="ml-1 h-4 w-4" />
                          </Link>
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            {/* Advisory Tab */}
            <TabsContent value="advisory" className="space-y-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="flex items-center text-lg">
                    <MessageCircle className="mr-2 h-5 w-5 text-blue-500" />
                    Yêu cầu tư vấn của bạn
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {advisoryRequests && advisoryRequests.length > 0 ? (
                    <div className="space-y-3">
                      {advisoryRequests.map((advisory, index) => (
                        <Link href={`/advisory/${advisory.id}`} key={index}>
                          <div className="flex flex-col space-y-3 rounded-lg border p-3 transition-all hover:border-blue-200 hover:bg-blue-50">
                            <div className="flex items-center justify-between">
                              <div className="font-medium">{advisory.title}</div>
                              {getAdvisoryStatusBadge(advisory.status)}
                            </div>
                            <div className="flex flex-col space-y-1 text-sm text-muted-foreground">
                              <div className="flex items-center justify-between">
                                <div className="flex items-center">
                                  <Clock className="mr-2 h-4 w-4" />
                                  {formatDate(advisory.created_at)}
                                </div>
                                <div>{getUrgencyBadge(advisory.urgency_level)}</div>
                              </div>
                              <div className="flex items-center">
                                <User className="mr-2 h-4 w-4" />
                                <div className="flex items-center">
                                  {advisory.assigned_doctor_id ? (
                                    <>
                                      <Avatar className="mr-2 h-6 w-6">
                                        <AvatarImage src="/caring-doctor.png" />
                                        <AvatarFallback>BS</AvatarFallback>
                                      </Avatar>
                                      <span>Bác sĩ đã được phân công</span>
                                    </>
                                  ) : (
                                    <span>Chưa có bác sĩ phụ trách</span>
                                  )}
                                </div>
                              </div>
                            </div>
                          </div>
                        </Link>
                      ))}
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center py-8 text-center">
                      <FileQuestion className="mb-2 h-10 w-10 text-muted-foreground" />
                      <p className="mb-2 text-lg font-medium">Không có yêu cầu tư vấn nào</p>
                      <p className="mb-4 text-sm text-muted-foreground">
                        Bạn chưa có yêu cầu tư vấn nào. Hãy tạo yêu cầu tư vấn mới.
                      </p>
                      <Button asChild>
                        <Link href="/advisory/request">Yêu cầu tư vấn</Link>
                      </Button>
                    </div>
                  )}
                  {advisoryRequests && advisoryRequests.length > 0 && (
                    <div className="mt-4 flex justify-end">
                      <Button variant="ghost" size="sm" asChild>
                        <Link href="/advisory" className="flex items-center">
                          Xem tất cả yêu cầu tư vấn
                          <ChevronRight className="ml-1 h-4 w-4" />
                        </Link>
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="flex items-center text-lg">
                    <HelpCircle className="mr-2 h-5 w-5 text-blue-500" />
                    Tạo yêu cầu tư vấn mới
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <p className="text-muted-foreground">
                      Bạn có thể tạo yêu cầu tư vấn mới để được bác sĩ chuyên khoa hỗ trợ giải đáp thắc mắc về tình
                      trạng sức khỏe của bạn.
                    </p>
                    <div className="grid gap-4 md:grid-cols-3">
                      <div className="rounded-lg border p-3">
                        <div className="mb-2 font-medium">Tư vấn thông thường</div>
                        <p className="mb-4 text-sm text-muted-foreground">
                          Dành cho các câu hỏi không khẩn cấp về tình trạng sức khỏe.
                        </p>
                        <Button variant="outline" size="sm" className="w-full" asChild>
                          <Link href="/advisory/request?type=regular">Tạo yêu cầu</Link>
                        </Button>
                      </div>
                      <div className="rounded-lg border p-3">
                        <div className="mb-2 font-medium">Tư vấn khẩn cấp</div>
                        <p className="mb-4 text-sm text-muted-foreground">
                          Dành cho các vấn đề cần được giải đáp nhanh chóng.
                        </p>
                        <Button variant="outline" size="sm" className="w-full" asChild>
                          <Link href="/advisory/request?type=urgent">Tạo yêu cầu</Link>
                        </Button>
                      </div>
                      <div className="rounded-lg border p-3">
                        <div className="mb-2 font-medium">Tư vấn theo dõi</div>
                        <p className="mb-4 text-sm text-muted-foreground">
                          Dành cho việc theo dõi sau điều trị hoặc tư vấn trước đó.
                        </p>
                        <Button variant="outline" size="sm" className="w-full" asChild>
                          <Link href="/advisory/request?type=followup">Tạo yêu cầu</Link>
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Medications Tab */}
            <TabsContent value="medications" className="space-y-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="flex items-center text-lg">
                    <Pill className="mr-2 h-5 w-5 text-blue-500" />
                    Thuốc hôm nay
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {activePrescriptions && activePrescriptions.length > 0 ? (
                    <div className="space-y-3">
                      {activePrescriptions.map((prescription, index) => (
                        <div key={index} className="rounded-lg border p-3">
                          <div className="flex items-center justify-between mb-2">
                            <div className="font-medium">
                              Đơn thuốc #{prescription.id.slice(0, 8)}
                            </div>
                            <Badge variant="outline" className="bg-blue-50 text-blue-700">
                              {prescription.status}
                            </Badge>
                          </div>
                          <div className="text-sm text-muted-foreground">
                            <div className="flex items-center">
                              <Clock className="mr-2 h-4 w-4" />
                              Ngày kê: {formatDate(prescription.prescribed_date)}
                            </div>
                            {prescription.prescription_medications && prescription.prescription_medications.length > 0 && (
                              <div className="mt-2">
                                <p className="font-medium">Thuốc:</p>
                                <ul className="list-disc list-inside ml-4">
                                  {prescription.prescription_medications.slice(0, 3).map((med, medIndex) => (
                                    <li key={medIndex}>
                                      {med.medication_name} - {med.dosage} ({med.frequency})
                                    </li>
                                  ))}
                                  {prescription.prescription_medications.length > 3 && (
                                    <li>... và {prescription.prescription_medications.length - 3} thuốc khác</li>
                                  )}
                                </ul>
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center py-8 text-center">
                      <Pill className="mb-2 h-10 w-10 text-muted-foreground" />
                      <p className="mb-2 text-lg font-medium">Không có đơn thuốc nào</p>
                      <p className="text-sm text-muted-foreground">
                        Hiện tại bạn không có đơn thuốc nào đang hiệu lực.
                      </p>
                    </div>
                  )}
                  <div className="mt-4 flex justify-end">
                    <Button variant="ghost" size="sm" asChild>
                      <Link href="/prescriptions" className="flex items-center">
                        Xem tất cả đơn thuốc
                        <ChevronRight className="ml-1 h-4 w-4" />
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          {/* Quick Access Section - Simplified */}
          <div className="mb-6">
            <h2 className="mb-4 text-xl font-bold">Truy cập nhanh</h2>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <Link href="/symptoms" className="group">
                <Card className="h-full transition-all hover:border-blue-500 hover:shadow-md">
                  <CardContent className="flex flex-col items-center justify-center p-4 text-center">
                    <ClipboardList className="mb-2 h-8 w-8 text-blue-500" />
                    <p className="font-medium">Theo dõi triệu chứng</p>
                  </CardContent>
                </Card>
              </Link>
              <Link href="/messages" className="group">
                <Card className="h-full transition-all hover:border-blue-500 hover:shadow-md">
                  <CardContent className="flex flex-col items-center justify-center p-4 text-center">
                    <MessageSquare className="mb-2 h-8 w-8 text-blue-500" />
                    <p className="font-medium">Tin nhắn</p>
                  </CardContent>
                </Card>
              </Link>
              <Link href="/knowledge" className="group">
                <Card className="h-full transition-all hover:border-blue-500 hover:shadow-md">
                  <CardContent className="flex flex-col items-center justify-center p-4 text-center">
                    <FileText className="mb-2 h-8 w-8 text-blue-500" />
                    <p className="font-medium">Thư viện kiến thức</p>
                  </CardContent>
                </Card>
              </Link>
              <Link href="/connect" className="group">
                <Card className="h-full transition-all hover:border-blue-500 hover:shadow-md">
                  <CardContent className="flex flex-col items-center justify-center p-4 text-center">
                    <User className="mb-2 h-8 w-8 text-blue-500" />
                    <p className="font-medium">Cộng đồng</p>
                  </CardContent>
                </Card>
              </Link>
            </div>
          </div>
        </>
      )}

      {userData.role === "medical_staff" && (
        <>
          {/* Main dashboard content for doctor - Simplified with tabs */}
          <Tabs defaultValue="overview" className="mb-8">
            <TabsList className="mb-4 grid w-full grid-cols-2 md:w-auto">
              <TabsTrigger value="overview">Tổng quan</TabsTrigger>
              <TabsTrigger value="consultations">Tư vấn & Bệnh nhân</TabsTrigger>
            </TabsList>

            {/* Overview Tab */}
            <TabsContent value="overview" className="space-y-4">
              {/* Doctor summary - Simplified */}
              {roleSpecificData && (
                <Card className="border-l-4 border-l-blue-500">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">Thông tin bác sĩ</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                      <div>
                        <p className="text-sm font-medium">Chuyên khoa</p>
                        <p className="text-sm text-muted-foreground">
                          {roleSpecificData?.specialization || "Chưa có thông tin"}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm font-medium">Bệnh viện</p>
                        <p className="text-sm text-muted-foreground">
                          {roleSpecificData?.hospital || "Chưa có thông tin"}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm font-medium">Khoa</p>
                        <p className="text-sm text-muted-foreground">
                          {roleSpecificData?.department || "Chưa có thông tin"}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm font-medium">Trạng thái xác minh</p>
                        <p className="text-sm text-muted-foreground">
                          {roleSpecificData?.is_verified ? "Đã xác minh" : "Chưa xác minh"}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Quick Actions for Doctor - Simplified */}
              <div className="grid grid-cols-2 gap-4 md:grid-cols-4">
                <Link href="/doctor/patient-links" className="group">
                  <Card className="h-full transition-all hover:border-blue-500 hover:shadow-md">
                    <CardContent className="flex flex-col items-center justify-center p-4 text-center">
                      <User className="mb-2 h-8 w-8 text-blue-500" />
                      <p className="font-medium">Quản lý bệnh nhân</p>
                    </CardContent>
                  </Card>
                </Link>
                <Link href="/consultations/doctor" className="group">
                  <Card className="h-full transition-all hover:border-blue-500 hover:shadow-md">
                    <CardContent className="flex flex-col items-center justify-center p-4 text-center">
                      <MessageSquare className="mb-2 h-8 w-8 text-blue-500" />
                      <p className="font-medium">Lịch tư vấn</p>
                    </CardContent>
                  </Card>
                </Link>
                <Link href="/advisory" className="group">
                  <Card className="h-full transition-all hover:border-blue-500 hover:shadow-md">
                    <CardContent className="flex flex-col items-center justify-center p-4 text-center">
                      <HelpCircle className="mb-2 h-8 w-8 text-blue-500" />
                      <p className="font-medium">Yêu cầu tư vấn</p>
                    </CardContent>
                  </Card>
                </Link>
                <Link href="/doctor/patient-links" className="group">
                  <Card className="h-full transition-all hover:border-blue-500 hover:shadow-md">
                    <CardContent className="flex flex-col items-center justify-center p-4 text-center">
                      <Pill className="mb-2 h-8 w-8 text-blue-500" />
                      <p className="font-medium">Kê đơn thuốc</p>
                    </CardContent>
                  </Card>
                </Link>
              </div>

              {/* Doctor Statistics - Simplified */}
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Bệnh nhân liên kết</CardTitle>
                    <User className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{linkedPatientsCount}</div>
                    <p className="text-xs text-muted-foreground">Bệnh nhân đang theo dõi</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Tư vấn hôm nay</CardTitle>
                    <MessageSquare className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{todayConsultationsCount}</div>
                    <p className="text-xs text-muted-foreground">Buổi tư vấn hôm nay</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Yêu cầu chờ</CardTitle>
                    <Clock className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{pendingAdvisoryCount}</div>
                    <p className="text-xs text-muted-foreground">Yêu cầu tư vấn chưa xử lý</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Đơn thuốc đã kê</CardTitle>
                    <Pill className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{activePrescriptionsCount}</div>
                    <p className="text-xs text-muted-foreground">Đơn thuốc đang hiệu lực</p>
                  </CardContent>
                </Card>
              </div>

              {/* Doctor Performance Metrics - Similar to patient health metrics */}
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="flex items-center text-lg">
                    <Activity className="mr-2 h-5 w-5 text-blue-500" />
                    Thống kê hiệu suất
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="flex items-center justify-between rounded-lg border p-3">
                      <div className="flex items-center gap-2">
                        <MessageSquare className="h-4 w-4 text-blue-500" />
                        <span className="font-medium">Tư vấn tuần này</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-blue-500">{weeklyConsultationsCount} buổi</span>
                      </div>
                    </div>
                    <div className="flex items-center justify-between rounded-lg border p-3">
                      <div className="flex items-center gap-2">
                        <Check className="h-4 w-4 text-emerald-500" />
                        <span className="font-medium">Tư vấn hoàn thành</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-emerald-500">{completedAdvisoryCount} yêu cầu</span>
                      </div>
                    </div>
                    <div className="flex items-center justify-between rounded-lg border p-3">
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4 text-amber-500" />
                        <span className="font-medium">Bệnh nhân theo dõi</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-amber-500">{linkedPatientsCount} bệnh nhân</span>
                      </div>
                    </div>
                    <div className="flex items-center justify-between rounded-lg border p-3">
                      <div className="flex items-center gap-2">
                        <Pill className="h-4 w-4 text-rose-500" />
                        <span className="font-medium">Đơn thuốc hiệu lực</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-rose-500">{activePrescriptionsCount} đơn</span>
                      </div>
                    </div>
                  </div>
                  <div className="mt-4 flex justify-end">
                    <Button variant="ghost" size="sm" asChild>
                      <Link href="/consultations/doctor" className="flex items-center">
                        Xem lịch tư vấn
                        <ChevronRight className="ml-1 h-4 w-4" />
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Doctor Work Progress - Similar to treatment progress */}
              {(todayConsultationsCount > 0 || pendingAdvisoryCount > 0) && (
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="flex items-center text-lg">
                      <Calendar className="mr-2 h-5 w-5 text-blue-500" />
                      Tiến độ công việc
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {todayConsultationsCount > 0 && (
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span>Tư vấn hôm nay</span>
                            <span className="font-medium">{todayConsultationsCount} buổi</span>
                          </div>
                          <Progress value={Math.min((todayConsultationsCount / 8) * 100, 100)} className="h-2" />
                          <p className="text-xs text-muted-foreground">Đã hoàn thành {todayConsultationsCount} buổi tư vấn</p>
                        </div>
                      )}

                      {pendingAdvisoryCount > 0 && (
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span>Yêu cầu tư vấn chờ xử lý</span>
                            <span className="font-medium">{pendingAdvisoryCount} yêu cầu</span>
                          </div>
                          <Progress value={Math.max(100 - (pendingAdvisoryCount * 10), 0)} className="h-2" />
                          <p className="text-xs text-muted-foreground">
                            {pendingAdvisoryCount > 5 ? "Cần ưu tiên xử lý" : "Trong tầm kiểm soát"}
                          </p>
                        </div>
                      )}

                      <div className="mt-4 flex justify-end">
                        <Button variant="ghost" size="sm" asChild>
                          <Link href="/advisory" className="flex items-center">
                            Xem yêu cầu tư vấn
                            <ChevronRight className="ml-1 h-4 w-4" />
                          </Link>
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            {/* Consultations & Patients Combined Tab */}
            <TabsContent value="consultations" className="space-y-4">
              {/* Advisory Requests */}
              {advisoryRequests && advisoryRequests.length > 0 && (
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="flex items-center text-lg">
                      <MessageSquare className="mr-2 h-5 w-5 text-blue-500" />
                      Yêu cầu tư vấn gần đây
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {advisoryRequests.slice(0, 3).map((advisory, index) => (
                        <Link href={`/advisory/${advisory.id}`} key={index}>
                          <div className="flex flex-col space-y-2 rounded-lg border p-3 transition-all hover:border-blue-200 hover:bg-blue-50">
                            <div className="flex items-center justify-between">
                              <div className="font-medium text-sm">{advisory.title}</div>
                              {getAdvisoryStatusBadge(advisory.status)}
                            </div>
                            <div className="flex items-center justify-between text-xs text-muted-foreground">
                              <span>Bệnh nhân: {advisory.patients?.full_name || "Không xác định"}</span>
                              <span>{formatDate(advisory.created_at)}</span>
                            </div>
                          </div>
                        </Link>
                      ))}
                    </div>
                    <div className="mt-4 flex justify-end">
                      <Button variant="ghost" size="sm" asChild>
                        <Link href="/advisory" className="flex items-center">
                          Xem tất cả
                          <ChevronRight className="ml-1 h-4 w-4" />
                        </Link>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Recent Prescriptions */}
              {activePrescriptions && activePrescriptions.length > 0 && (
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="flex items-center text-lg">
                      <Pill className="mr-2 h-5 w-5 text-blue-500" />
                      Đơn thuốc gần đây
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {activePrescriptions.slice(0, 3).map((prescription, index) => (
                        <div key={index} className="rounded-lg border p-3">
                          <div className="flex items-center justify-between mb-2">
                            <div className="font-medium text-sm">
                              {prescription.patients?.full_name || "Không xác định"}
                            </div>
                            <Badge variant="outline" className="bg-blue-50 text-blue-700 text-xs">
                              {prescription.status}
                            </Badge>
                          </div>
                          <div className="text-xs text-muted-foreground">
                            <span>Ngày kê: {formatDate(prescription.prescribed_date)}</span>
                            {prescription.prescription_medications && prescription.prescription_medications.length > 0 && (
                              <span className="ml-4">
                                {prescription.prescription_medications.length} loại thuốc
                              </span>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                    <div className="mt-4 flex justify-end">
                      <Button variant="ghost" size="sm" asChild>
                        <Link href="/doctor/patient-links" className="flex items-center">
                          Xem tất cả
                          <ChevronRight className="ml-1 h-4 w-4" />
                        </Link>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Empty state when no data */}
              {(!advisoryRequests || advisoryRequests.length === 0) && (!activePrescriptions || activePrescriptions.length === 0) && (
                <Card>
                  <CardContent className="flex flex-col items-center justify-center py-12 text-center">
                    <MessageSquare className="mb-4 h-12 w-12 text-muted-foreground" />
                    <p className="mb-2 text-lg font-medium">Chưa có hoạt động nào</p>
                    <p className="text-sm text-muted-foreground mb-4">
                      Bạn chưa có yêu cầu tư vấn hoặc đơn thuốc nào.
                    </p>
                    <Button asChild>
                      <Link href="/doctor/patient-links">Quản lý bệnh nhân</Link>
                    </Button>
                  </CardContent>
                </Card>
              )}
            </TabsContent>
          </Tabs>

          {/* Quick Access Section for Doctor */}
          <div className="mb-6">
            <h2 className="mb-4 text-xl font-bold">Truy cập nhanh</h2>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <Link href="/doctor/patient-links" className="group">
                <Card className="h-full transition-all hover:border-blue-500 hover:shadow-md">
                  <CardContent className="flex flex-col items-center justify-center p-4 text-center">
                    <User className="mb-2 h-8 w-8 text-blue-500" />
                    <p className="font-medium">Quản lý bệnh nhân</p>
                  </CardContent>
                </Card>
              </Link>
              <Link href="/consultations/doctor" className="group">
                <Card className="h-full transition-all hover:border-blue-500 hover:shadow-md">
                  <CardContent className="flex flex-col items-center justify-center p-4 text-center">
                    <MessageSquare className="mb-2 h-8 w-8 text-blue-500" />
                    <p className="font-medium">Lịch tư vấn</p>
                  </CardContent>
                </Card>
              </Link>
              <Link href="/advisory" className="group">
                <Card className="h-full transition-all hover:border-blue-500 hover:shadow-md">
                  <CardContent className="flex flex-col items-center justify-center p-4 text-center">
                    <HelpCircle className="mb-2 h-8 w-8 text-blue-500" />
                    <p className="font-medium">Yêu cầu tư vấn</p>
                  </CardContent>
                </Card>
              </Link>
              <Link href="/messages" className="group">
                <Card className="h-full transition-all hover:border-blue-500 hover:shadow-md">
                  <CardContent className="flex flex-col items-center justify-center p-4 text-center">
                    <MessageCircle className="mb-2 h-8 w-8 text-blue-500" />
                    <p className="font-medium">Tin nhắn</p>
                  </CardContent>
                </Card>
              </Link>
            </div>
          </div>
        </>
      )}

      {userData.role === "admin" && (
        <div>
          <h2 className="text-2xl font-bold tracking-tight mb-4">Bảng điều khiển quản trị viên</h2>
          {/* Nội dung dành cho quản trị viên */}
        </div>
      )}
    </div>
  )
}
