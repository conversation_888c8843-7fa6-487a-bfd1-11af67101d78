import { createClient } from "@/lib/supabase/server"
import { redirect } from "next/navigation"
import Link from "next/link"
import { format } from "date-fns"
import { vi } from "date-fns/locale"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Progress } from "@/components/ui/progress"
import {
  FileText,
  Pill,
  Activity,
  MessageSquare,
  ChevronRight,
  Heart,
  Thermometer,
  TrendingUp,
  User,
  Check,
  BarChart3,
  ClipboardList,
  HelpCircle,
  FileQuestion,
  MessageCircle,
  Clock,
} from "lucide-react"

export default async function DashboardPage() {
  const supabase = createClient()

  const {
    data: { session },
  } = await supabase.auth.getSession()

  if (!session) {
    redirect("/login")
  }

  // Get the authenticated user
  const { data: userData, error: userError } = await supabase
    .from("users")
    .select("*")
    .eq("id", session.user.id)
    .single()

  if (userError || !userData) {
    console.error("Error fetching user data:", userError)
    redirect("/login")
  }

  // Fetch additional data based on user role
  let roleSpecificData = null
  if (userData.role === "patient") {
    const { data: patientData } = await supabase.from("patients").select("*").eq("id", userData.id).single()
    roleSpecificData = patientData
  } else if (userData.role === "medical_staff") {
    const { data: medicalStaffData } = await supabase.from("medical_staff").select("*").eq("user_id", userData.id).single()
    roleSpecificData = medicalStaffData
  }

  // Fetch advisory requests for patient or doctor
  let advisoryRequests = []
  if (userData.role === "patient") {
    const { data: advisories } = await supabase
      .from("advisory_requests")
      .select(`
        id,
        title,
        created_at,
        status,
        urgency_level,
        assigned_doctor_id
      `)
      .eq("patient_id", userData.id)
      .order("created_at", { ascending: false })
      .limit(3)

    advisoryRequests = advisories || []
  } else if (userData.role === "medical_staff" && roleSpecificData) {
    const { data: advisories } = await supabase
      .from("advisory_requests")
      .select(`
        id,
        title,
        created_at,
        status,
        urgency_level,
        patient_id,
        patients!inner(full_name)
      `)
      .eq("assigned_doctor_id", roleSpecificData.id)
      .order("created_at", { ascending: false })
      .limit(3)

    advisoryRequests = advisories || []
  }

  // Fetch active prescriptions for patient or linked patients for doctor
  let activePrescriptions = []
  if (userData.role === "patient") {
    const { data: prescriptions } = await supabase
      .from("prescriptions")
      .select(`
        id,
        prescribed_date,
        status,
        prescription_medications(id, medication_name, dosage, frequency, instructions)
      `)
      .eq("patient_id", userData.id)
      .eq("status", "active")
      .order("prescribed_date", { ascending: false })
      .limit(3)

    activePrescriptions = prescriptions || []
  } else if (userData.role === "medical_staff" && roleSpecificData) {
    const { data: prescriptions } = await supabase
      .from("prescriptions")
      .select(`
        id,
        prescribed_date,
        status,
        patient_id,
        patients!inner(full_name),
        prescription_medications(id, medication_name, dosage, frequency, instructions)
      `)
      .eq("prescribed_by", roleSpecificData.id)
      .eq("status", "active")
      .order("prescribed_date", { ascending: false })
      .limit(3)

    activePrescriptions = prescriptions || []
  }

  // Fetch recent health metrics for patient or linked patients for doctor
  let recentHealthMetrics = []
  let linkedPatientsCount = 0
  if (userData.role === "patient") {
    const { data: metrics } = await supabase
      .from("health_metrics")
      .select("*")
      .eq("patient_id", userData.id)
      .order("recorded_at", { ascending: false })
      .limit(4)

    recentHealthMetrics = metrics || []
  } else if (userData.role === "medical_staff" && roleSpecificData) {
    // Get linked patients count
    const { count } = await supabase
      .from("doctor_patient_links")
      .select("*", { count: "exact", head: true })
      .eq("doctor_id", roleSpecificData.id)
      .eq("status", "approved")

    linkedPatientsCount = count || 0
  }

  // Default health metrics if none found
  const defaultHealthMetrics = [
    {
      name: "Huyết áp",
      value: 120,
      unit: "mmHg",
      status: "normal",
      change: -5,
      icon: <Heart className="h-4 w-4 text-rose-500" />,
    },
    {
      name: "Nhịp tim",
      value: 72,
      unit: "bpm",
      status: "normal",
      change: 0,
      icon: <Activity className="h-4 w-4 text-emerald-500" />,
    },
    {
      name: "Nhiệt độ",
      value: 36.5,
      unit: "°C",
      status: "normal",
      change: 0.2,
      icon: <Thermometer className="h-4 w-4 text-amber-500" />,
    },
    {
      name: "Cân nặng",
      value: 65,
      unit: "kg",
      status: "improving",
      change: -1.5,
      icon: <TrendingUp className="h-4 w-4 text-blue-500" />,
    },
  ]

  // Default medications if none found
  const defaultMedications = [
    {
      id: "1",
      name: "Paracetamol",
      dosage: "500mg",
      time: "08:00",
      instructions: "Uống sau bữa sáng",
      taken: true,
      overdue: false,
    },
    {
      id: "2",
      name: "Vitamin D",
      dosage: "1000 IU",
      time: "12:00",
      instructions: "Uống cùng bữa trưa",
      taken: false,
      overdue: false,
    },
    {
      id: "3",
      name: "Amoxicillin",
      dosage: "500mg",
      time: "20:00",
      instructions: "Uống trước khi đi ngủ",
      taken: false,
      overdue: false,
    },
  ]

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return "Chưa xác định"
    return format(new Date(dateString), "dd/MM/yyyy", { locale: vi })
  }

  // Format time for display
  const formatTime = (dateString) => {
    if (!dateString) return ""
    return format(new Date(dateString), "HH:mm", { locale: vi })
  }

  // Get status color
  const getStatusColor = (status) => {
    switch (status) {
      case "normal":
        return "text-emerald-500"
      case "warning":
        return "text-amber-500"
      case "critical":
        return "text-rose-500"
      case "improving":
        return "text-blue-500"
      default:
        return "text-gray-500"
    }
  }

  // Get advisory status badge
  const getAdvisoryStatusBadge = (status) => {
    switch (status) {
      case "pending":
        return (
          <Badge variant="outline" className="bg-amber-50 text-amber-700">
            Đang chờ
          </Badge>
        )
      case "assigned":
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700">
            Đã phân công
          </Badge>
        )
      case "in_progress":
        return (
          <Badge variant="outline" className="bg-purple-50 text-purple-700">
            Đang xử lý
          </Badge>
        )
      case "completed":
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700">
            Hoàn thành
          </Badge>
        )
      case "cancelled":
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700">
            Đã hủy
          </Badge>
        )
      default:
        return <Badge variant="outline">Không xác định</Badge>
    }
  }

  // Get urgency level badge
  const getUrgencyBadge = (level) => {
    switch (level) {
      case "low":
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Thấp</Badge>
      case "medium":
        return <Badge className="bg-amber-100 text-amber-800 hover:bg-amber-100">Trung bình</Badge>
      case "high":
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-100">Cao</Badge>
      default:
        return <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-100">Không xác định</Badge>
    }
  }

  return (
    <div className="container mx-auto py-4">
      {/* Welcome section - Simplified */}
      <div className="mb-6 flex flex-col space-y-2">
        <h1 className="text-2xl font-bold">Xin chào, {userData.full_name}</h1>
        <p className="text-muted-foreground">Chào mừng bạn quay trở lại với OncoCare.VN</p>
      </div>

      {userData.role === "patient" && (
        <>
          {/* Main dashboard content - Simplified with tabs */}
          <Tabs defaultValue="overview" className="mb-8">
            <TabsList className="mb-4 grid w-full grid-cols-3 md:w-auto">
              <TabsTrigger value="overview">Tổng quan</TabsTrigger>
              <TabsTrigger value="advisory">Tư vấn</TabsTrigger>
              <TabsTrigger value="medications">Thuốc</TabsTrigger>
            </TabsList>

            {/* Overview Tab */}
            <TabsContent value="overview" className="space-y-4">
              {/* Patient summary - Simplified */}
              {roleSpecificData && (
                <Card className="border-l-4 border-l-blue-500">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">Thông tin bệnh nhân</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                      <div>
                        <p className="text-sm font-medium">Chẩn đoán</p>
                        <p className="text-sm text-muted-foreground">
                          {roleSpecificData?.diagnosis || "Chưa có thông tin"}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm font-medium">Loại ung thư</p>
                        <p className="text-sm text-muted-foreground">
                          {roleSpecificData?.cancer_type || "Chưa có thông tin"}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm font-medium">Giai đoạn</p>
                        <p className="text-sm text-muted-foreground">
                          {roleSpecificData?.cancer_stage || "Chưa có thông tin"}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm font-medium">Trạng thái điều trị</p>
                        <p className="text-sm text-muted-foreground">
                          {roleSpecificData?.treatment_status || "Chưa có thông tin"}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Quick Actions - Simplified */}
              <div className="grid grid-cols-2 gap-4 md:grid-cols-4">
                <Link href="/advisory/request" className="group">
                  <Card className="h-full transition-all hover:border-blue-500 hover:shadow-md">
                    <CardContent className="flex flex-col items-center justify-center p-4 text-center">
                      <HelpCircle className="mb-2 h-8 w-8 text-blue-500" />
                      <p className="font-medium">Yêu cầu tư vấn</p>
                    </CardContent>
                  </Card>
                </Link>
                <Link href="/prescriptions" className="group">
                  <Card className="h-full transition-all hover:border-blue-500 hover:shadow-md">
                    <CardContent className="flex flex-col items-center justify-center p-4 text-center">
                      <Pill className="mb-2 h-8 w-8 text-blue-500" />
                      <p className="font-medium">Đơn thuốc</p>
                    </CardContent>
                  </Card>
                </Link>
                <Link href="/medical-records" className="group">
                  <Card className="h-full transition-all hover:border-blue-500 hover:shadow-md">
                    <CardContent className="flex flex-col items-center justify-center p-4 text-center">
                      <FileText className="mb-2 h-8 w-8 text-blue-500" />
                      <p className="font-medium">Hồ sơ y tế</p>
                    </CardContent>
                  </Card>
                </Link>
                <Link href="/health-metrics" className="group">
                  <Card className="h-full transition-all hover:border-blue-500 hover:shadow-md">
                    <CardContent className="flex flex-col items-center justify-center p-4 text-center">
                      <BarChart3 className="mb-2 h-8 w-8 text-blue-500" />
                      <p className="font-medium">Chỉ số sức khỏe</p>
                    </CardContent>
                  </Card>
                </Link>
              </div>

              {/* Health Metrics - Simplified */}
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="flex items-center text-lg">
                    <Activity className="mr-2 h-5 w-5 text-blue-500" />
                    Chỉ số sức khỏe gần đây
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-4 md:grid-cols-2">
                    {defaultHealthMetrics.map((metric, index) => (
                      <div key={index} className="flex items-center justify-between rounded-lg border p-3">
                        <div className="flex items-center gap-2">
                          {metric.icon}
                          <span className="font-medium">{metric.name}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className={getStatusColor(metric.status)}>
                            {metric.value} {metric.unit}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                  <div className="mt-4 flex justify-end">
                    <Button variant="ghost" size="sm" asChild>
                      <Link href="/health-metrics" className="flex items-center">
                        Xem tất cả
                        <ChevronRight className="ml-1 h-4 w-4" />
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Treatment Progress - Simplified */}
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="flex items-center text-lg">
                    <Activity className="mr-2 h-5 w-5 text-blue-500" />
                    Tiến trình điều trị
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Tiến độ tổng thể</span>
                        <span className="font-medium">45%</span>
                      </div>
                      <Progress value={45} className="h-2" />
                    </div>

                    <div className="space-y-2">
                      <p className="font-medium">Giai đoạn hiện tại: Hóa trị</p>
                      <p className="text-sm text-muted-foreground">Điều trị hóa chất để tiêu diệt tế bào ung thư</p>
                      <div className="flex justify-between text-sm">
                        <span>Tiến độ giai đoạn</span>
                        <span className="font-medium">60%</span>
                      </div>
                      <Progress value={60} className="h-2" />
                    </div>

                    <div className="mt-4 flex justify-end">
                      <Button variant="ghost" size="sm" asChild>
                        <Link href="/treatment" className="flex items-center">
                          Chi tiết điều trị
                          <ChevronRight className="ml-1 h-4 w-4" />
                        </Link>
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Advisory Tab */}
            <TabsContent value="advisory" className="space-y-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="flex items-center text-lg">
                    <MessageCircle className="mr-2 h-5 w-5 text-blue-500" />
                    Yêu cầu tư vấn của bạn
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {advisoryRequests && advisoryRequests.length > 0 ? (
                    <div className="space-y-3">
                      {advisoryRequests.map((advisory, index) => (
                        <Link href={`/advisory/${advisory.id}`} key={index}>
                          <div className="flex flex-col space-y-3 rounded-lg border p-3 transition-all hover:border-blue-200 hover:bg-blue-50">
                            <div className="flex items-center justify-between">
                              <div className="font-medium">{advisory.title}</div>
                              {getAdvisoryStatusBadge(advisory.status)}
                            </div>
                            <div className="flex flex-col space-y-1 text-sm text-muted-foreground">
                              <div className="flex items-center justify-between">
                                <div className="flex items-center">
                                  <Clock className="mr-2 h-4 w-4" />
                                  {formatDate(advisory.created_at)}
                                </div>
                                <div>{getUrgencyBadge(advisory.urgency_level)}</div>
                              </div>
                              <div className="flex items-center">
                                <User className="mr-2 h-4 w-4" />
                                <div className="flex items-center">
                                  {advisory.assigned_doctor_id ? (
                                    <>
                                      <Avatar className="mr-2 h-6 w-6">
                                        <AvatarImage src="/caring-doctor.png" />
                                        <AvatarFallback>BS</AvatarFallback>
                                      </Avatar>
                                      <span>Bác sĩ đã được phân công</span>
                                    </>
                                  ) : (
                                    <span>Chưa có bác sĩ phụ trách</span>
                                  )}
                                </div>
                              </div>
                            </div>
                          </div>
                        </Link>
                      ))}
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center py-8 text-center">
                      <FileQuestion className="mb-2 h-10 w-10 text-muted-foreground" />
                      <p className="mb-2 text-lg font-medium">Không có yêu cầu tư vấn nào</p>
                      <p className="mb-4 text-sm text-muted-foreground">
                        Bạn chưa có yêu cầu tư vấn nào. Hãy tạo yêu cầu tư vấn mới.
                      </p>
                      <Button asChild>
                        <Link href="/advisory/request">Yêu cầu tư vấn</Link>
                      </Button>
                    </div>
                  )}
                  {advisoryRequests && advisoryRequests.length > 0 && (
                    <div className="mt-4 flex justify-end">
                      <Button variant="ghost" size="sm" asChild>
                        <Link href="/advisory" className="flex items-center">
                          Xem tất cả yêu cầu tư vấn
                          <ChevronRight className="ml-1 h-4 w-4" />
                        </Link>
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="flex items-center text-lg">
                    <HelpCircle className="mr-2 h-5 w-5 text-blue-500" />
                    Tạo yêu cầu tư vấn mới
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <p className="text-muted-foreground">
                      Bạn có thể tạo yêu cầu tư vấn mới để được bác sĩ chuyên khoa hỗ trợ giải đáp thắc mắc về tình
                      trạng sức khỏe của bạn.
                    </p>
                    <div className="grid gap-4 md:grid-cols-3">
                      <div className="rounded-lg border p-3">
                        <div className="mb-2 font-medium">Tư vấn thông thường</div>
                        <p className="mb-4 text-sm text-muted-foreground">
                          Dành cho các câu hỏi không khẩn cấp về tình trạng sức khỏe.
                        </p>
                        <Button variant="outline" size="sm" className="w-full" asChild>
                          <Link href="/advisory/request?type=regular">Tạo yêu cầu</Link>
                        </Button>
                      </div>
                      <div className="rounded-lg border p-3">
                        <div className="mb-2 font-medium">Tư vấn khẩn cấp</div>
                        <p className="mb-4 text-sm text-muted-foreground">
                          Dành cho các vấn đề cần được giải đáp nhanh chóng.
                        </p>
                        <Button variant="outline" size="sm" className="w-full" asChild>
                          <Link href="/advisory/request?type=urgent">Tạo yêu cầu</Link>
                        </Button>
                      </div>
                      <div className="rounded-lg border p-3">
                        <div className="mb-2 font-medium">Tư vấn theo dõi</div>
                        <p className="mb-4 text-sm text-muted-foreground">
                          Dành cho việc theo dõi sau điều trị hoặc tư vấn trước đó.
                        </p>
                        <Button variant="outline" size="sm" className="w-full" asChild>
                          <Link href="/advisory/request?type=followup">Tạo yêu cầu</Link>
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Medications Tab */}
            <TabsContent value="medications" className="space-y-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="flex items-center text-lg">
                    <Pill className="mr-2 h-5 w-5 text-blue-500" />
                    Thuốc hôm nay
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {defaultMedications.map((medication) => (
                      <div
                        key={medication.id}
                        className={`flex items-center justify-between rounded-lg border p-3 ${medication.taken ? "bg-muted/50" : ""
                          } ${medication.overdue ? "border-red-200 bg-red-50" : ""}`}
                      >
                        <div className="space-y-1">
                          <div className="flex items-center">
                            <span className="font-medium">{medication.name}</span>
                            <span className="ml-2 text-sm text-muted-foreground">({medication.dosage})</span>
                          </div>
                          <div className="flex items-center text-sm text-muted-foreground">
                            <Clock className="mr-1 h-3 w-3" />
                            <span>{medication.time}</span>
                            {medication.instructions && <span className="ml-2">- {medication.instructions}</span>}
                          </div>
                        </div>
                        <div>
                          {medication.taken ? (
                            <div className="flex items-center text-emerald-500">
                              <Check className="mr-1 h-4 w-4" />
                              <span className="text-xs">Đã uống</span>
                            </div>
                          ) : (
                            <Button size="sm" variant={medication.overdue ? "destructive" : "outline"}>
                              <Check className="mr-1 h-3 w-3" />
                              <span>Đánh dấu</span>
                            </Button>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                  <div className="mt-4 flex justify-end">
                    <Button variant="ghost" size="sm" asChild>
                      <Link href="/prescriptions" className="flex items-center">
                        Xem tất cả đơn thuốc
                        <ChevronRight className="ml-1 h-4 w-4" />
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          {/* Quick Access Section - Simplified */}
          <div className="mb-6">
            <h2 className="mb-4 text-xl font-bold">Truy cập nhanh</h2>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <Link href="/symptoms" className="group">
                <Card className="h-full transition-all hover:border-blue-500 hover:shadow-md">
                  <CardContent className="flex flex-col items-center justify-center p-4 text-center">
                    <ClipboardList className="mb-2 h-8 w-8 text-blue-500" />
                    <p className="font-medium">Theo dõi triệu chứng</p>
                  </CardContent>
                </Card>
              </Link>
              <Link href="/messages" className="group">
                <Card className="h-full transition-all hover:border-blue-500 hover:shadow-md">
                  <CardContent className="flex flex-col items-center justify-center p-4 text-center">
                    <MessageSquare className="mb-2 h-8 w-8 text-blue-500" />
                    <p className="font-medium">Tin nhắn</p>
                  </CardContent>
                </Card>
              </Link>
              <Link href="/knowledge" className="group">
                <Card className="h-full transition-all hover:border-blue-500 hover:shadow-md">
                  <CardContent className="flex flex-col items-center justify-center p-4 text-center">
                    <FileText className="mb-2 h-8 w-8 text-blue-500" />
                    <p className="font-medium">Thư viện kiến thức</p>
                  </CardContent>
                </Card>
              </Link>
              <Link href="/connect" className="group">
                <Card className="h-full transition-all hover:border-blue-500 hover:shadow-md">
                  <CardContent className="flex flex-col items-center justify-center p-4 text-center">
                    <User className="mb-2 h-8 w-8 text-blue-500" />
                    <p className="font-medium">Cộng đồng</p>
                  </CardContent>
                </Card>
              </Link>
            </div>
          </div>
        </>
      )}

      {userData.role === "medical_staff" && (
        <>
          {/* Main dashboard content for doctor - Simplified with tabs */}
          <Tabs defaultValue="overview" className="mb-8">
            <TabsList className="mb-4 grid w-full grid-cols-3 md:w-auto">
              <TabsTrigger value="overview">Tổng quan</TabsTrigger>
              <TabsTrigger value="consultations">Tư vấn</TabsTrigger>
              <TabsTrigger value="patients">Bệnh nhân</TabsTrigger>
            </TabsList>

            {/* Overview Tab */}
            <TabsContent value="overview" className="space-y-4">
              {/* Doctor summary - Simplified */}
              {roleSpecificData && (
                <Card className="border-l-4 border-l-green-500">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">Thông tin bác sĩ</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                      <div>
                        <p className="text-sm font-medium">Chuyên khoa</p>
                        <p className="text-sm text-muted-foreground">
                          {roleSpecificData?.specialization || "Chưa có thông tin"}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm font-medium">Bệnh viện</p>
                        <p className="text-sm text-muted-foreground">
                          {roleSpecificData?.hospital || "Chưa có thông tin"}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm font-medium">Khoa</p>
                        <p className="text-sm text-muted-foreground">
                          {roleSpecificData?.department || "Chưa có thông tin"}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm font-medium">Kinh nghiệm</p>
                        <p className="text-sm text-muted-foreground">
                          {roleSpecificData?.years_of_experience ? `${roleSpecificData.years_of_experience} năm` : "Chưa có thông tin"}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Quick Actions for Doctor - Simplified */}
              <div className="grid grid-cols-2 gap-4 md:grid-cols-4">
                <Link href="/doctor/patient-links" className="group">
                  <Card className="h-full transition-all hover:border-green-500 hover:shadow-md">
                    <CardContent className="flex flex-col items-center justify-center p-4 text-center">
                      <User className="mb-2 h-8 w-8 text-green-500" />
                      <p className="font-medium">Quản lý bệnh nhân</p>
                    </CardContent>
                  </Card>
                </Link>
                <Link href="/consultations/doctor" className="group">
                  <Card className="h-full transition-all hover:border-green-500 hover:shadow-md">
                    <CardContent className="flex flex-col items-center justify-center p-4 text-center">
                      <MessageSquare className="mb-2 h-8 w-8 text-green-500" />
                      <p className="font-medium">Lịch tư vấn</p>
                    </CardContent>
                  </Card>
                </Link>
                <Link href="/advisory" className="group">
                  <Card className="h-full transition-all hover:border-green-500 hover:shadow-md">
                    <CardContent className="flex flex-col items-center justify-center p-4 text-center">
                      <HelpCircle className="mb-2 h-8 w-8 text-green-500" />
                      <p className="font-medium">Yêu cầu tư vấn</p>
                    </CardContent>
                  </Card>
                </Link>
                <Link href="/messages" className="group">
                  <Card className="h-full transition-all hover:border-green-500 hover:shadow-md">
                    <CardContent className="flex flex-col items-center justify-center p-4 text-center">
                      <MessageCircle className="mb-2 h-8 w-8 text-green-500" />
                      <p className="font-medium">Tin nhắn</p>
                    </CardContent>
                  </Card>
                </Link>
              </div>

              {/* Doctor Statistics - Simplified */}
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Bệnh nhân liên kết</CardTitle>
                    <User className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{linkedPatientsCount}</div>
                    <p className="text-xs text-muted-foreground">Bệnh nhân đang theo dõi</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Tư vấn hôm nay</CardTitle>
                    <MessageSquare className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">3</div>
                    <p className="text-xs text-muted-foreground">Buổi tư vấn đã lên lịch</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Yêu cầu chờ</CardTitle>
                    <Clock className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{advisoryRequests.length}</div>
                    <p className="text-xs text-muted-foreground">Yêu cầu tư vấn chưa xử lý</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Đơn thuốc</CardTitle>
                    <Pill className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{activePrescriptions.length}</div>
                    <p className="text-xs text-muted-foreground">Đơn thuốc đang hiệu lực</p>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* Consultations Tab */}
            <TabsContent value="consultations" className="space-y-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="flex items-center text-lg">
                    <MessageSquare className="mr-2 h-5 w-5 text-green-500" />
                    Yêu cầu tư vấn được phân công
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {advisoryRequests && advisoryRequests.length > 0 ? (
                    <div className="space-y-3">
                      {advisoryRequests.map((advisory, index) => (
                        <Link href={`/advisory/${advisory.id}`} key={index}>
                          <div className="flex flex-col space-y-3 rounded-lg border p-3 transition-all hover:border-green-200 hover:bg-green-50">
                            <div className="flex items-center justify-between">
                              <div className="font-medium">{advisory.title}</div>
                              {getAdvisoryStatusBadge(advisory.status)}
                            </div>
                            <div className="flex flex-col space-y-1 text-sm text-muted-foreground">
                              <div className="flex items-center justify-between">
                                <div className="flex items-center">
                                  <Clock className="mr-2 h-4 w-4" />
                                  {formatDate(advisory.created_at)}
                                </div>
                                <div>{getUrgencyBadge(advisory.urgency_level)}</div>
                              </div>
                              <div className="flex items-center">
                                <User className="mr-2 h-4 w-4" />
                                <span>Bệnh nhân: {advisory.patients?.full_name || "Không xác định"}</span>
                              </div>
                            </div>
                          </div>
                        </Link>
                      ))}
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center py-8 text-center">
                      <MessageSquare className="mb-2 h-10 w-10 text-muted-foreground" />
                      <p className="mb-2 text-lg font-medium">Không có yêu cầu tư vấn nào</p>
                      <p className="text-sm text-muted-foreground">
                        Hiện tại không có yêu cầu tư vấn nào được phân công cho bạn.
                      </p>
                    </div>
                  )}
                  {advisoryRequests && advisoryRequests.length > 0 && (
                    <div className="mt-4 flex justify-end">
                      <Button variant="ghost" size="sm" asChild>
                        <Link href="/advisory" className="flex items-center">
                          Xem tất cả yêu cầu tư vấn
                          <ChevronRight className="ml-1 h-4 w-4" />
                        </Link>
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            {/* Patients Tab */}
            <TabsContent value="patients" className="space-y-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="flex items-center text-lg">
                    <User className="mr-2 h-5 w-5 text-green-500" />
                    Đơn thuốc gần đây
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {activePrescriptions && activePrescriptions.length > 0 ? (
                    <div className="space-y-3">
                      {activePrescriptions.map((prescription, index) => (
                        <div key={index} className="rounded-lg border p-3">
                          <div className="flex items-center justify-between mb-2">
                            <div className="font-medium">
                              Bệnh nhân: {prescription.patients?.full_name || "Không xác định"}
                            </div>
                            <Badge variant="outline" className="bg-green-50 text-green-700">
                              {prescription.status}
                            </Badge>
                          </div>
                          <div className="text-sm text-muted-foreground">
                            <div className="flex items-center">
                              <Clock className="mr-2 h-4 w-4" />
                              Ngày kê: {formatDate(prescription.prescribed_date)}
                            </div>
                            {prescription.prescription_medications && prescription.prescription_medications.length > 0 && (
                              <div className="mt-2">
                                <p className="font-medium">Thuốc:</p>
                                <ul className="list-disc list-inside ml-4">
                                  {prescription.prescription_medications.slice(0, 2).map((med, medIndex) => (
                                    <li key={medIndex}>
                                      {med.medication_name} - {med.dosage} ({med.frequency})
                                    </li>
                                  ))}
                                  {prescription.prescription_medications.length > 2 && (
                                    <li>... và {prescription.prescription_medications.length - 2} thuốc khác</li>
                                  )}
                                </ul>
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center py-8 text-center">
                      <Pill className="mb-2 h-10 w-10 text-muted-foreground" />
                      <p className="mb-2 text-lg font-medium">Không có đơn thuốc nào</p>
                      <p className="text-sm text-muted-foreground">
                        Hiện tại không có đơn thuốc nào đang hiệu lực.
                      </p>
                    </div>
                  )}
                  {activePrescriptions && activePrescriptions.length > 0 && (
                    <div className="mt-4 flex justify-end">
                      <Button variant="ghost" size="sm" asChild>
                        <Link href="/doctor/patient-links" className="flex items-center">
                          Xem tất cả bệnh nhân
                          <ChevronRight className="ml-1 h-4 w-4" />
                        </Link>
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          {/* Quick Access Section for Doctor - Simplified */}
          <div className="mb-6">
            <h2 className="mb-4 text-xl font-bold">Truy cập nhanh</h2>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <Link href="/doctor/patient-links" className="group">
                <Card className="h-full transition-all hover:border-green-500 hover:shadow-md">
                  <CardContent className="flex flex-col items-center justify-center p-4 text-center">
                    <User className="mb-2 h-8 w-8 text-green-500" />
                    <p className="font-medium">Quản lý bệnh nhân</p>
                  </CardContent>
                </Card>
              </Link>
              <Link href="/consultations/doctor" className="group">
                <Card className="h-full transition-all hover:border-green-500 hover:shadow-md">
                  <CardContent className="flex flex-col items-center justify-center p-4 text-center">
                    <MessageSquare className="mb-2 h-8 w-8 text-green-500" />
                    <p className="font-medium">Lịch tư vấn</p>
                  </CardContent>
                </Card>
              </Link>
              <Link href="/advisory" className="group">
                <Card className="h-full transition-all hover:border-green-500 hover:shadow-md">
                  <CardContent className="flex flex-col items-center justify-center p-4 text-center">
                    <HelpCircle className="mb-2 h-8 w-8 text-green-500" />
                    <p className="font-medium">Yêu cầu tư vấn</p>
                  </CardContent>
                </Card>
              </Link>
              <Link href="/messages" className="group">
                <Card className="h-full transition-all hover:border-green-500 hover:shadow-md">
                  <CardContent className="flex flex-col items-center justify-center p-4 text-center">
                    <MessageCircle className="mb-2 h-8 w-8 text-green-500" />
                    <p className="font-medium">Tin nhắn</p>
                  </CardContent>
                </Card>
              </Link>
            </div>
          </div>
        </>
      )}

      {userData.role === "admin" && (
        <div>
          <h2 className="text-2xl font-bold tracking-tight mb-4">Bảng điều khiển quản trị viên</h2>
          {/* Nội dung dành cho quản trị viên */}
        </div>
      )}
    </div>
  )
}
